"""
File utility functions for the ADC evaluation system.

This module provides centralized file operations with proper error handling
and logging to avoid code duplication across the system.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

from ..exceptions import FileSystemError, PaperNotFoundError
from ..models import GroundTruthDocument, ExtractionRow, ExtractedEndpoint
from .constants import Constants


logger = logging.getLogger(__name__)


class FileUtils:
    """Utility class for file operations."""
    
    @staticmethod
    def get_paper_file_paths(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> Dict[str, Path]:
        """Get all file paths for a given paper ID."""
        base_path = Path(data_dir)

        # Try to find the correct case for ground truth file
        gt_dir = base_path / Constants.GROUND_TRUTH_DIR
        gt_file = None
        if gt_dir.exists():
            # Try exact case first
            exact_gt = gt_dir / f"{paper_id}_gt{Constants.JSON_EXT}"
            if exact_gt.exists():
                gt_file = exact_gt
            else:
                # Try lowercase
                lower_gt = gt_dir / f"{paper_id.lower()}_gt{Constants.JSON_EXT}"
                if lower_gt.exists():
                    gt_file = lower_gt
                else:
                    # Try uppercase
                    upper_gt = gt_dir / f"{paper_id.upper()}_gt{Constants.JSON_EXT}"
                    if upper_gt.exists():
                        gt_file = upper_gt
                    else:
                        # Default to original if not found
                        gt_file = exact_gt
        else:
            gt_file = gt_dir / f"{paper_id}_gt{Constants.JSON_EXT}"

        return {
            "ground_truth": gt_file,
            "results": base_path / Constants.RESULTS_DIR / f"{paper_id}_results{Constants.JSON_EXT}",
            "evaluation": base_path / Constants.EVALUATIONS_DIR / f"{paper_id}_evaluation{Constants.JSON_EXT}"
        }
    
    @staticmethod
    def check_paper_files_exist(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> Tuple[bool, List[str]]:
        """Check if all required files exist for a paper."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        missing_files = []
        
        for file_type, path in paths.items():
            if file_type != "evaluation" and not path.exists():  # evaluation file is created, not required
                missing_files.append(str(path))
        
        return len(missing_files) == 0, missing_files
    
    @staticmethod
    def load_json_file(file_path: Path) -> Dict[str, Any]:
        """Load and parse JSON file with error handling."""
        try:
            if not file_path.exists():
                raise FileSystemError(f"File not found: {file_path}")
            
            logger.debug(f"Loading JSON file: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
            
        except json.JSONDecodeError as e:
            raise FileSystemError(
                f"Invalid JSON in file {file_path}: {str(e)}", 
                {"file_path": str(file_path), "json_error": str(e)}
            )
        except Exception as e:
            raise FileSystemError(
                f"Error loading file {file_path}: {str(e)}", 
                {"file_path": str(file_path)}
            )
    
    @staticmethod
    def save_json_file(data: Dict[str, Any], file_path: Path) -> None:
        """Save data to JSON file with error handling."""
        try:
            logger.debug(f"Saving JSON file: {file_path}")
            
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Successfully saved file: {file_path}")
            
        except Exception as e:
            raise FileSystemError(
                f"Error saving file {file_path}: {str(e)}", 
                {"file_path": str(file_path)}
            )
    
    @staticmethod
    @staticmethod
    def load_ground_truth(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> Dict[str, Any]:
        """Load ground truth data for a specific paper."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        gt_path = paths["ground_truth"]

        if not gt_path.exists():
            raise PaperNotFoundError(paper_id, [str(gt_path)])

        try:
            data = FileUtils.load_json_file(gt_path)

            # ADD paper_id to the data structure if missing
            if 'paper_id' not in data:
                data['paper_id'] = paper_id
                logger.debug(f"Added missing paper_id to ground truth: {paper_id}")

            # REMOVE OR COMMENT OUT this validation since your data doesn't have paper_id
            # # Validate paper_id matches (case-insensitive)
            # data_paper_id = data.get('paper_id', 'unknown')
            # if data_paper_id.lower() != paper_id.lower():
            #     raise FileSystemError(
            #         f"Paper ID mismatch: file contains '{data_paper_id}', expected '{paper_id}'",
            #         {"expected": paper_id, "found": data_paper_id}
            #     )

            # Find the records/endpoints array
            records_key = None
            for key in ['records', 'endpoints', 'data']:
                if key in data and isinstance(data[key], list):
                    records_key = key
                    break
            
            if not records_key:
                # If no array found, assume the data itself is the array
                if isinstance(data, list):
                    data = {'paper_id': paper_id, 'records': data}
                    records_key = 'records'
                else:
                    raise FileSystemError(f"No records array found in ground truth for {paper_id}")

            # Add paper_id and id to each record
            if records_key and data[records_key]:
                for i, record in enumerate(data[records_key]):
                    # Add paper_id to each record if missing
                    if 'paper_id' not in record:
                        record['paper_id'] = paper_id
                    
                    # Add id field if missing
                    if not record.get('id'):
                        if record.get('gt_id'):
                            record['id'] = record['gt_id']
                        else:
                            record['id'] = f"{paper_id}_gt_{i}"
                        logger.debug(f"Added missing id to GT record {i}: {record['id']}")

            # Count records for logging
            record_count = len(data.get(records_key, []) if records_key else [])
            logger.info(f"Loaded ground truth for paper {paper_id}: {record_count} records")
            return data

        except Exception as e:
            if isinstance(e, (PaperNotFoundError, FileSystemError)):
                raise
            raise FileSystemError(f"Error loading ground truth for paper {paper_id}: {str(e)}")
    
    @staticmethod
    def load_extraction_results(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> List[Tuple[ExtractedEndpoint, Dict[str, Any]]]:
        """Load extraction results for a specific paper."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        results_path = paths["results"]

        if not results_path.exists():
            raise PaperNotFoundError(paper_id, [str(results_path)])

        try:
            data = FileUtils.load_json_file(results_path)
            
            # ADD THIS DEBUG SECTION
            logger.info(f"Raw JSON data type: {type(data)}")
            logger.info(f"Raw JSON data length: {len(data) if isinstance(data, (list, dict)) else 'N/A'}")
            logger.info(f"First few items: {data[:2] if isinstance(data, list) and len(data) > 0 else data}")
            
            # Check if data is empty
            if not data:
                logger.warning(f"Empty extraction data for paper {paper_id}")
                return []
                
            # Check if data is not a list
            if not isinstance(data, list):
                logger.error(f"Expected list but got {type(data)} for paper {paper_id}")
                return []
            
            extraction_rows = []
            from pydantic import ValidationError
            from ..exceptions import DataLoadingError

            for i, row_data in enumerate(data):
                # Skip rows that don't belong to this paper
                paper_id_field = row_data.get("paper_id", row_data.get("id", ""))
                if paper_id_field and paper_id_field.lower() != paper_id.lower():
                    continue
                
                try:
                    model_input_data = row_data.copy()

                    # Skip ADC-level records that don't have endpoint information
                    if model_input_data.get('endpoint_name') is None:
                        logger.debug(f"Skipping ADC-level record {i} in paper {paper_id} - no endpoint information")
                        continue

                    # Ensure model_name is not None for endpoint records
                    if model_input_data.get('model_name') is None:
                        model_input_data['model_name'] = "unspecified"

                    if 'measurements' in model_input_data and 'endpoint_measurements' not in model_input_data:
                        model_input_data['endpoint_measurements'] = model_input_data.pop('measurements')

                    if 'endpoint_measurements' not in model_input_data:
                        measurement_data = {
                            'measurement_id': f"{paper_id}_{model_input_data.get('_row_index', i)}",
                            'value': {'value': model_input_data.get('measured_value'), 'citation': model_input_data.get('endpoint_citations')},
                            'dose': {'value': model_input_data.get('measured_dose'), 'unit': 'unspecified'} if model_input_data.get('measured_dose') is not None else None,
                            'concentration': {'value': model_input_data.get('measured_concentration'), 'unit': 'unspecified'} if model_input_data.get('measured_concentration') is not None else None,
                            'time_point': {'value': model_input_data.get('measured_timepoint'), 'unit': 'hours'} if model_input_data.get('measured_timepoint') is not None else None,
                            'death_percentage': {'value': model_input_data.get('measured_death_percentage')} if model_input_data.get('measured_death_percentage') is not None else None
                        }
                        model_input_data['endpoint_measurements'] = [{k: v for k, v in measurement_data.items() if v is not None}]

                    model_input_data.setdefault('type', 'endpoint')

                    pydantic_model = ExtractedEndpoint(**model_input_data)
                    
                    extraction_rows.append((pydantic_model, row_data))
                    
                except ValidationError as e:
                    error_msg = f"Pydantic validation failed for row {i} in paper {paper_id}: {e}"
                    logger.error(error_msg)
                    logger.debug(f"Problematic row data: {row_data}")
                    raise DataLoadingError(error_msg)
                except Exception as e:
                    error_msg = f"An unexpected error occurred while parsing row {i} in paper {paper_id}: {e}"
                    logger.error(error_msg)
                    logger.debug(f"Problematic row data: {row_data}")
                    raise DataLoadingError(error_msg)
            
            logger.info(f"Loaded extraction results for paper {paper_id}: {len(extraction_rows)} endpoints")
            return extraction_rows
            
        except Exception as e:
            if isinstance(e, (PaperNotFoundError, FileSystemError)):
                raise
            raise FileSystemError(f"Error loading extraction results for paper {paper_id}: {str(e)}")
    
    @staticmethod
    async def save_evaluation_results(results: Dict[str, Any], paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> str:
        """Save evaluation results to file."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        evaluation_path = paths["evaluation"]
        
        await FileUtils.save_json_file(results, evaluation_path)
        return str(evaluation_path)